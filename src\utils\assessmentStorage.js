/**
 * Assessment Storage Utility
 * 
 * Manages localStorage for assessment answers to provide persistence
 * across page refreshes and navigation between assessment phases.
 */

const STORAGE_KEY = 'assessmentAnswers';

/**
 * Get all saved assessment answers from localStorage
 * @returns {Object} All saved answers or empty object if none exist
 */
export const getAssessmentAnswers = () => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    return saved ? JSON.parse(saved) : {};
  } catch (error) {
    console.warn('Failed to load assessment answers from localStorage:', error);
    return {};
  }
};

/**
 * Save a single answer to localStorage
 * @param {string} questionKey - The question key (e.g., "via_creativity_0")
 * @param {number} value - The answer value (1-5)
 */
export const saveAssessmentAnswer = (questionKey, value) => {
  try {
    const currentAnswers = getAssessmentAnswers();
    const updatedAnswers = {
      ...currentAnswers,
      [questionKey]: value
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedAnswers));
  } catch (error) {
    console.warn('Failed to save assessment answer to localStorage:', error);
  }
};

/**
 * Save multiple answers to localStorage
 * @param {Object} answers - Object containing question keys and values
 */
export const saveAssessmentAnswers = (answers) => {
  try {
    const currentAnswers = getAssessmentAnswers();
    const updatedAnswers = {
      ...currentAnswers,
      ...answers
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedAnswers));
  } catch (error) {
    console.warn('Failed to save assessment answers to localStorage:', error);
  }
};

/**
 * Get answers for a specific assessment type
 * @param {string} assessmentType - The assessment type (e.g., "via", "riasec", "bigFive")
 * @returns {Object} Filtered answers for the specified assessment
 */
export const getAnswersByAssessment = (assessmentType) => {
  const allAnswers = getAssessmentAnswers();
  const filteredAnswers = {};
  
  Object.keys(allAnswers).forEach(key => {
    if (key.startsWith(assessmentType)) {
      filteredAnswers[key] = allAnswers[key];
    }
  });
  
  return filteredAnswers;
};

/**
 * Clear all assessment answers from localStorage
 * Usually called after successful submission
 */
export const clearAssessmentAnswers = () => {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear assessment answers from localStorage:', error);
  }
};

/**
 * Check if there are any saved answers
 * @returns {boolean} True if there are saved answers
 */
export const hasAssessmentAnswers = () => {
  const answers = getAssessmentAnswers();
  return Object.keys(answers).length > 0;
};

/**
 * Get the count of answered questions for a specific assessment
 * @param {string} assessmentType - The assessment type (e.g., "via", "riasec", "bigFive")
 * @returns {number} Number of answered questions
 */
export const getAnsweredCount = (assessmentType) => {
  const answers = getAnswersByAssessment(assessmentType);
  return Object.keys(answers).length;
};

/**
 * Remove answers for a specific assessment type
 * @param {string} assessmentType - The assessment type to remove
 */
export const clearAssessmentByType = (assessmentType) => {
  try {
    const allAnswers = getAssessmentAnswers();
    const filteredAnswers = {};
    
    Object.keys(allAnswers).forEach(key => {
      if (!key.startsWith(assessmentType)) {
        filteredAnswers[key] = allAnswers[key];
      }
    });
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredAnswers));
  } catch (error) {
    console.warn('Failed to clear assessment by type from localStorage:', error);
  }
};
